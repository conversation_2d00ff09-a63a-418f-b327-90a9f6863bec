import { type Doc } from '../../convex/_generated/dataModel';

/**
 * A map where keys are client IDs (string) and values are their attendance history.
 * This uses a string for client IDs.
 */
export type ClientAttendanceHistory = Map<string, Doc<'attendance_records'>[]>;

/**
 * A map to store the first attendance record for each client.
 * Keys are client IDs (string).
 */
export type ClientFirstAttendanceMap = Map<string, Doc<'attendance_records'>>;

/**
 * Defines the core metrics for an instructor.
 */
export interface InstructorMetrics {
  /** The percentage of class spots filled. Range: 0-100. */
  fillRate: number;
  /** The percentage of new clients who return. Range: 0-100. */
  retention: number;
  /** Average client satisfaction rating. Range: 1-5. */
  satisfaction: number;
  /** The number of new clients acquired. */
  acquisition: number;
  /** A weighted composite score of performance. Range: 0-100. */
  performanceScore: number;
}

/**
 * Extends core metrics with performance status, trend, and historical data.
 */
export interface InstructorPerformance extends InstructorMetrics {
  /** The unique identifier for the instructor. */
  instructorId: string;
  /** The instructor's full name. */
  name: string;
  /** The calculated performance status of the instructor. */
  status: 'top_performer' | 'rising_star' | 'stable' | 'declining';
  /** The performance trend based on recent changes. */
  trend: 'rising' | 'stable' | 'falling';
  /** The percentage change in performance score compared to the previous month. */
  monthOverMonthChange: number;
  /** An array of the last 30 days' performance scores. */
  last30Days: { date: string; score: number }[];
  /** The eligibility status for being displayed on the dashboard. */
  eligibilityStatus: { isEligible: boolean; reason: string };
}

/**
 * Weights for calculating the composite performance score, based on industry standards.
 */
export const PERFORMANCE_WEIGHTS = {
  /** 25% - Direct revenue impact */
  fillRate: 0.25,
  /** 35% - Most cost-effective growth */
  retention: 0.35,
  /** 30% - Drives retention and referrals */
  satisfaction: 0.3,
  /** 10% - Valuable but expensive */
  acquisition: 0.1,
};

/**
 * Thresholds for determining instructor status based on month-over-month performance change.
 */
export const STATUS_THRESHOLDS = {
  /** A month-over-month improvement greater than 10% qualifies as a rising star. */
  RISING: 10,
  /** A month-over-month decline of more than 5% is considered declining. */
  DECLINING: -5,
};
