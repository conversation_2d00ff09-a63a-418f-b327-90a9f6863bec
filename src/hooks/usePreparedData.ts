/**
 * @file This file contains the `usePreparedData` hook, which fetches raw
 * instructor data from the Convex backend and processes it into the format
 * required by the performance dashboard components.
 */
import { useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import type {
  InstructorPerformance,
  InstructorMetrics,
} from '@/types/instructor';
import {
  calculatePerformanceScore,
  determineStatus,
  determineTrend,
} from '@/lib/performanceCalculations';

/**
 * A custom hook to fetch and prepare instructor performance data.
 * It encapsulates data fetching, transformation, and memoization.
 * @returns {InstructorPerformance[]} An array of prepared instructor data.
 */
export const usePreparedData = (): InstructorPerformance[] => {
  const rawInstructors = useQuery(api.instructors.getPerformanceDashboardData);

  const preparedData = useMemo(() => {
    if (!rawInstructors) {
      return [];
    }

    // HACK: The `instructors` table only contains name and ID. All performance
    // metrics are currently mocked here on the client-side to allow for UI
    // development. A future step is to calculate these metrics in a Convex
    // query by joining with `classes`, `ratings`, etc.
    return rawInstructors.map((instructor) => {
      // Create plausible, but fake, metrics for each instructor.
      const retention = 60 + Math.random() * 35; // 60-95%
      const fillRate = 70 + Math.random() * 28; // 70-98%
      const satisfaction = 4.2 + Math.random() * 0.7; // 4.2-4.9
      const acquisition = 5 + Math.random() * 15; // 5-20

      const metrics: InstructorMetrics = {
        retention,
        fillRate,
        satisfaction,
        acquisition,
      };

      const performanceScore = calculatePerformanceScore(metrics);
      const monthOverMonthChange = (Math.random() - 0.5) * 15; // -7.5 to +7.5
      const status = determineStatus(monthOverMonthChange, performanceScore);
      const trend = determineTrend(monthOverMonthChange);

      return {
        instructorId: instructor.instructor_id,
        name: instructor.name,
        ...metrics,
        performanceScore,
        status,
        trend,
        monthOverMonthChange,
        last30Days: Array.from({ length: 30 }, (_, i) => ({
          date: `Day ${i + 1}`,
          score: performanceScore - 15 + i * Math.random(),
        })),
        eligibilityStatus: { isEligible: true, reason: '' },
      };
    });
  }, [rawInstructors]);

  return preparedData;
};
