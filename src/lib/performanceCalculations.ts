import {
  type InstructorMetrics,
  type InstructorPerformance,
  PERFORMANCE_WEIGHTS,
  STATUS_THRESHOLDS,
} from '../types/instructor';

/**
 * Calculates a weighted performance score for an instructor based on various metrics.
 * Normalizes acquisition and satisfaction to a 0-100 scale before applying weights.
 *
 * @param metrics The instructor's raw performance metrics.
 * @returns The calculated performance score, a number between 0 and 100.
 */
export const calculatePerformanceScore = (
  metrics: InstructorMetrics
): number => {
  // Normalize acquisition to 0-100 scale (assume 10 new clients/month is excellent)
  const normalizedAcquisition = Math.min((metrics.acquisition / 10) * 100, 100);

  // Normalize satisfaction from 1-5 to 0-100
  const normalizedSatisfaction = ((metrics.satisfaction - 1) / 4) * 100;

  const score =
    metrics.fillRate * PERFORMANCE_WEIGHTS.fillRate +
    metrics.retention * PERFORMANCE_WEIGHTS.retention +
    normalizedSatisfaction * PERFORMANCE_WEIGHTS.satisfaction +
    normalizedAcquisition * PERFORMANCE_WEIGHTS.acquisition;

  return Math.max(0, Math.min(score, 100)); // Clamp score between 0 and 100
};

/**
 * Determines the performance status of an instructor based on their score and recent trend.
 *
 * @param monthOverMonthChange The percentage change in performance score from the previous month.
 * @param currentScore The instructor's current performance score.
 * @returns The instructor's performance status.
 */
export const determineStatus = (
  monthOverMonthChange: number,
  currentScore: number
): InstructorPerformance['status'] => {
  if (currentScore >= 85 && monthOverMonthChange >= 0) return 'top_performer';
  if (monthOverMonthChange > STATUS_THRESHOLDS.RISING) return 'rising_star';
  if (monthOverMonthChange < STATUS_THRESHOLDS.DECLINING) return 'declining';
  return 'stable';
};

/**
 * Determines the performance trend based on month-over-month change.
 *
 * @param monthOverMonthChange The percentage change in performance score.
 * @returns The performance trend ('rising', 'falling', or 'stable').
 */
export const determineTrend = (
  monthOverMonthChange: number
): InstructorPerformance['trend'] => {
  if (monthOverMonthChange > 5) return 'rising';
  if (monthOverMonthChange < -5) return 'falling';
  return 'stable';
};
