import { UserButton } from "@clerk/clerk-react";

const Header = () => {
  return (
    <header className="border-b">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-2">
           <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6"><path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-3.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 12.07 3H12a2 2 0 0 0-2 2v.17l-.82 1.2a2 2 0 0 1-1.66.9H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2Z"/><path d="M12 12v-2"/><path d="m15 15-2-3-2 3"/><path d="M9 12h6"/></svg>
           <span className="font-bold">Performance Hub</span>
        </div>
        <UserButton afterSignOutUrl="/" />
      </div>
    </header>
  );
};

export default Header;
