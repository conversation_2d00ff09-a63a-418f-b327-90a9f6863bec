import React from 'react';
import { Responsive<PERSON>ontainer, AreaChart, Area } from 'recharts';
import {
  ChartLineUp as Chart<PERSON>ineUpIcon,
  Users as UsersIcon,
  Star as StarIcon,
  TrendUp as TrendUpIcon,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';

// biome-ignore lint/suspicious/noExplicitAny: This is a Recharts component prop
type CustomTooltipProps = any;

/**
 * A custom tooltip component for the Recharts scatter plot.
 * It displays an instructor's name, performance score, a 30-day trend sparkline,
 * and key performance metrics.
 */
export const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (!active || !payload || !payload[0]) {
    return null;
  }

  const instructor: InstructorPerformance = payload[0].payload;

  return (
    <div className="custom-tooltip w-64 rounded-lg border border-gray-200 bg-white p-4 shadow-lg">
      <div className="tooltip-header mb-3 flex items-center justify-between border-b border-gray-200 pb-3">
        <h3 className="font-semibold text-gray-900">{instructor.name}</h3>
        <div className="performance-score flex items-baseline gap-1">
          <span className="text-2xl font-bold text-indigo-600">
            {Math.round(instructor.performanceScore)}
          </span>
          <span className="text-sm text-gray-500">/100</span>
        </div>
      </div>

      <div className="tooltip-sparkline my-2">
        <ResponsiveContainer width="100%" height={40}>
          <AreaChart data={instructor.last30Days}>
            <defs>
              <linearGradient
                id="sparkline-gradient"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.4} />
                <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0} />
              </linearGradient>
            </defs>
            <Area
              type="monotone"
              dataKey="score"
              stroke="#8b5cf6"
              fill="url(#sparkline-gradient)"
              strokeWidth={2}
            />
          </AreaChart>
        </ResponsiveContainer>
        <span className="mt-1 block text-center text-xs text-gray-500">
          30-day trend
        </span>
      </div>

      <div className="tooltip-metrics mt-3 flex flex-col gap-2">
        <div className="metric-row flex items-center justify-between text-sm">
          <span className="flex items-center gap-2 text-gray-600">
            <ChartLineUpIcon
              size={16}
              weight="duotone"
              className="text-blue-500"
            />
            Fill Rate
          </span>
          <span className="font-medium text-gray-900">
            {instructor.fillRate.toFixed(1)}%
          </span>
        </div>
        <div className="metric-row flex items-center justify-between text-sm">
          <span className="flex items-center gap-2 text-gray-600">
            <UsersIcon size={16} weight="duotone" className="text-green-500" />
            Retention
          </span>
          <span className="font-medium text-gray-900">
            {instructor.retention.toFixed(1)}%
          </span>
        </div>
        <div className="metric-row flex items-center justify-between text-sm">
          <span className="flex items-center gap-2 text-gray-600">
            <StarIcon size={16} weight="duotone" className="text-yellow-500" />
            Rating
          </span>
          <span className="font-medium text-gray-900">
            {instructor.satisfaction.toFixed(1)}/5
          </span>
        </div>
        <div className="metric-row flex items-center justify-between text-sm">
          <span className="flex items-center gap-2 text-gray-600">
            <TrendUpIcon
              size={16}
              weight="duotone"
              className="text-indigo-500"
            />
            MoM Change
          </span>
          <span
            className={`font-medium ${
              instructor.monthOverMonthChange >= 0
                ? 'text-green-600'
                : 'text-red-600'
            }`}
          >
            {instructor.monthOverMonthChange > 0 ? '+' : ''}
            {instructor.monthOverMonthChange.toFixed(1)}%
          </span>
        </div>
      </div>

      <div className="tooltip-footer mt-3 border-t border-gray-200 pt-3 text-center">
        <span className="text-xs font-medium text-indigo-600">
          Click for detailed analysis →
        </span>
      </div>
    </div>
  );
};
