/**
 * @file This file contains the StatusBar component, which displays a visual
 * distribution of instructor performance statuses.
 */
import type { InstructorPerformance } from '@/types/instructor';

/**
 * Props for the StatusBar component.
 * @property {InstructorPerformance[]} data - The array of prepared instructor performance data.
 */
interface StatusBarProps {
  data: InstructorPerformance[];
}

const STATUS_COLORS: Record<string, string> = {
  Exceptional: 'bg-green-500',
  'Exceeds Expectations': 'bg-blue-500',
  'Meets Expectations': 'bg-yellow-500',
  'Needs Improvement': 'bg-red-500',
};

const STATUS_ORDER = [
  'Exceptional',
  'Exceeds Expectations',
  'Meets Expectations',
  'Needs Improvement',
];

/**
 * A component that displays a segmented status bar showing the distribution
 * of instructor performance statuses.
 * @param {StatusBarProps} props - The component props.
 * @returns {JSX.Element} The rendered StatusBar component.
 */
export const StatusBar = ({ data }: StatusBarProps): JSX.Element => {
  const totalInstructors = data.length;

  const statusCounts = data.reduce((acc, instructor) => {
    acc[instructor.status] = (acc[instructor.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (totalInstructors === 0) {
    return (
      <div className="rounded-lg bg-card p-4 shadow">
        <h3 className="mb-2 text-lg font-semibold text-card-foreground">
          Instructor Status Distribution
        </h3>
        <div className="text-muted-foreground">No data available.</div>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-card p-4 shadow">
      <h3 className="mb-4 text-lg font-semibold text-card-foreground">
        Instructor Status Distribution
      </h3>
      <div className="flex h-4 w-full overflow-hidden rounded-full bg-muted">
        {STATUS_ORDER.map((status) => {
          const count = statusCounts[status] || 0;
          const percentage = (count / totalInstructors) * 100;
          if (percentage === 0) return null;
          return (
            <div
              key={status}
              style={{ width: `${percentage}%` }}
              className={`${STATUS_COLORS[status]}`}
              title={`${status}: ${count} (${percentage.toFixed(1)}%)`}
            />
          );
        })}
      </div>
      <div className="mt-4 flex flex-wrap justify-center gap-x-6 gap-y-2">
        {STATUS_ORDER.map((status) => (
          <div key={status} className="flex items-center">
            <span
              className={`mr-2 inline-block h-3 w-3 rounded-full ${STATUS_COLORS[status]}`}
            />
            <span className="text-sm text-muted-foreground">
              {status}: {statusCounts[status] || 0}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
