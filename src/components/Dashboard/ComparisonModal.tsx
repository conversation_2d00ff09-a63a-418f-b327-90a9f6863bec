import { X } from '@phosphor-icons/react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { PreparedInstructor } from '@/types/instructor';

export const ComparisonModal = ({
  instructors,
  onClose,
}: {
  instructors: PreparedInstructor[];
  onClose: () => void;
}) => {
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div
        className="modal-content comparison-modal"
        onClick={(e) => e.stopPropagation()}
      >
        <button className="modal-close" onClick={onClose}>
          <X size={20} />
        </button>

        <h2>Instructor Comparison</h2>

        {/* Side-by-side metrics table */}
        <div className="metrics-comparison">
          <table className="comparison-table">
            <thead>
              <tr>
                <th>Metric</th>
                {instructors.map((inst) => (
                  <th key={inst.id}>{inst.name}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Performance Score</td>
                {instructors.map((inst) => (
                  <td key={inst.id}>
                    {Math.round(inst.performanceScore || 0)}
                  </td>
                ))}
              </tr>
              <tr>
                <td>Utilization Score</td>
                {instructors.map((inst) => (
                  <td key={inst.id}>
                    {Math.round(inst.utilization?.score || 0)}
                  </td>
                ))}
              </tr>
              <tr>
                <td>Retention Score</td>
                {instructors.map((inst) => (
                  <td key={inst.id}>
                    {Math.round(inst.retention?.score || 0)}
                  </td>
                ))}
              </tr>
              <tr>
                <td>Satisfaction Score</td>
                {instructors.map((inst) => (
                  <td key={inst.id}>
                    {Math.round(inst.satisfaction?.score || 0)}
                  </td>
                ))}
              </tr>
              <tr>
                <td>Acquisition Score</td>
                {instructors.map((inst) => (
                  <td key={inst.id}>
                    {Math.round(inst.acquisition?.score || 0)}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
