.tooltip-footer {
  margin-top: 0.75rem;
  padding-top: 0.5rem;
  border-top: 1px solid #3a3a5a;
  font-size: 0.75rem;
  color: #9c9cff;
  text-align: center;
  opacity: 0.8;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(10, 10, 20, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a2e;
  border: 1px solid #5a5a78;
  border-radius: 12px;
  padding: 1.5rem;
  color: #e0e0fc;
  position: relative;
  max-width: 90vw;
  width: 600px;
}

.modal-content.comparison-modal {
  width: 800px;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #9c9cff;
  cursor: pointer;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #3a3a5a;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}

.detail-header h2,
.comparison-modal h2 {
  margin: 0;
  font-size: 1.5rem;
}

.compare-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #3a3a5a;
  border: 1px solid #5a5a78;
  color: #e0e0fc;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.compare-button:hover {
  background-color: #5a5a78;
}

/* Comparison Modal Table */
.metrics-comparison,
.trend-comparison {
  margin-top: 1.5rem;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #3a3a5a;
}

.comparison-table th {
  color: #9c9cff;
  font-weight: normal;
}

.comparison-table td {
  font-size: 1.125rem;
  font-weight: bold;
  text-align: center;
}

.comparison-table th:first-child,
.comparison-table td:first-child {
  text-align: left;
  font-weight: normal;
  font-size: 0.875rem;
}

.trend-comparison h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
}
