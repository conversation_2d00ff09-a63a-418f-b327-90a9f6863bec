import React, { useState } from 'react';
import {
  StarIcon,
  RocketLaunchIcon,
  EqualsIcon,
  WarningIcon,
  TrendUpIcon,
  TrendDownIcon,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';

// Note: The `payload` prop is provided by Recharts and contains the data item for this bubble.
// biome-ignore lint/suspicious/noExplicitAny: This is a Recharts component prop
type CustomBubbleProps = any & {
  payload: InstructorPerformance;
  compareMode: boolean;
  compareList: InstructorPerformance[];
};

/**
 * A custom component for rendering interactive bubbles in the Recharts scatter plot.
 * It displays instructor status, trend, and rating, with visual feedback on hover
 * and for different performance states.
 */
export const CustomBubble = (props: CustomBubbleProps) => {
  const { cx, cy, payload, compareMode, compareList } = props;
  const [isHovered, setIsHovered] = useState(false);

  // Calculate radius from rating (1-5 scale)
  const baseRadius = 15;
  const radius = baseRadius + (payload.satisfaction - 1) * 7; // Scales from 15 to 43

  // Determine colors and icons based on status
  const getStatusConfig = (status: InstructorPerformance['status']) => {
    switch (status) {
      case 'top_performer':
        return { color: '#f59e0b', icon: StarIcon, glowColor: '#fbbf24' };
      case 'rising_star':
        return {
          color: '#10b981',
          icon: RocketLaunchIcon,
          glowColor: '#34d399',
        };
      case 'declining':
        return { color: '#ef4444', icon: WarningIcon, glowColor: '#f87171' };
      default:
        return { color: '#3b82f6', icon: EqualsIcon, glowColor: '#60a5fa' };
    }
  };

  const config = getStatusConfig(payload.status);
  const Icon = config.icon;
  const TrendIcon =
    payload.trend === 'rising'
      ? TrendUpIcon
      : payload.trend === 'falling'
      ? TrendDownIcon
      : null;

  // Check if selected in compare mode
  const isSelected =
    compareMode &&
    compareList?.some(
      (inst: InstructorPerformance) =>
        inst.instructorId === payload.instructorId
    );

  return (
    <g
      transform={`translate(${cx}, ${cy})`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{ cursor: 'pointer' }}
      className="bubble-group"
    >
      {/* Outer glow for top performers or hover */}
      {(payload.status === 'top_performer' || isHovered) && (
        <circle
          r={radius + 8}
          fill="none"
          stroke={config.glowColor}
          strokeWidth="2"
          opacity={isHovered ? 0.8 : 0.4}
          className={
            payload.status === 'top_performer' ? 'pulse-animation' : ''
          }
        />
      )}

      {/* Selection ring in compare mode */}
      {isSelected && (
        <circle
          r={radius + 12}
          fill="none"
          stroke="#8b5cf6"
          strokeWidth="3"
          strokeDasharray="5 5"
          className="rotate-animation"
        />
      )}

      {/* Main bubble */}
      <circle
        r={radius}
        fill={config.color}
        fillOpacity={0.15}
        stroke={config.color}
        strokeWidth={isHovered ? 3 : 2}
        className="bubble-main"
      />

      {/* Inner circle for contrast */}
      <circle r={radius - 4} fill="white" fillOpacity={0.9} />

      {/* Status icon */}
      <g transform={`translate(${-12}, ${-12})`}>
        <Icon size={24} weight="duotone" color={config.color} />
      </g>

      {/* Trend indicator */}
      {TrendIcon && (
        <g transform={`translate(${radius - 15}, ${-radius + 5})`}>
          <circle r="12" fill="white" stroke={config.color} strokeWidth="2" />
          <g transform="translate(-8, -8)">
            <TrendIcon
              size={16}
              weight="bold"
              color={payload.trend === 'rising' ? '#10b981' : '#ef4444'}
            />
          </g>
        </g>
      )}

      {/* Rating indicator (bottom) */}
      <g transform={`translate(0, ${radius + 10})`}>
        <text
          textAnchor="middle"
          fill={config.color}
          fontSize="12"
          fontWeight="600"
        >
          {payload.satisfaction.toFixed(1)}★
        </text>
      </g>
    </g>
  );
};
