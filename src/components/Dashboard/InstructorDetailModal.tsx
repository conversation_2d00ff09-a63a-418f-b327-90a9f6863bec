import React, { useMemo } from 'react';
// We no longer need createPortal
// import { createPortal } from 'react-dom';
import {
  Responsive<PERSON>ontainer,
  RadialBar<PERSON>hart,
  RadialBar,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  AreaChart,
  Area,
} from 'recharts';
import {
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  X as XIcon,
  Star as StarIcon,
  RocketLaunch as RocketLaunchIcon,
  Equals as EqualsIcon,
  Warning as WarningIcon,
  Users as UsersIcon,
  ChartLine as ChartLineIcon,
  Clock as ClockIcon,
  TrendUp as TrendUpIcon,
  UserPlus as UserPlusIcon,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';

// --- Stubs for Helper Functions ---

// biome-ignore lint/suspicious/noExplicitAny: This is a mock data generator
const generateWeeklyPattern = (instructor: any) => [
  { day: 'Mon', morning: 0, afternoon: 0, evening: 0 },
  { day: 'Tue', morning: 80, afternoon: 90, evening: 75 },
  { day: 'Wed', morning: 85, afternoon: 0, evening: 0 },
  { day: 'Thu', morning: 70, afternoon: 80, evening: 65 },
  { day: 'Fri', morning: 90, afternoon: 0, evening: 88 },
  { day: 'Sat', morning: 95, afternoon: 92, evening: 0 },
  { day: 'Sun', morning: 88, afternoon: 85, evening: 0 },
];

const getStatusIcon = (status: InstructorPerformance['status']) => {
  const iconProps = { size: 16, weight: 'duotone' as const };
  switch (status) {
    case 'top_performer':
      return <StarIcon {...iconProps} />;
    case 'rising_star':
      return <RocketLaunchIcon {...iconProps} />;
    case 'declining':
      return <WarningIcon {...iconProps} />;
    default:
      return <EqualsIcon {...iconProps} />;
  }
};

const formatStatus = (status: InstructorPerformance['status']) => {
  return status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

// biome-ignore lint/suspicious/noExplicitAny: This is a mock data helper
const getBestTimeSlot = (weeklyData: any) => 'Saturday Morning';

// biome-ignore lint/suspicious/noExplicitAny: This is a mock data helper
const getRetentionComparison = (instructor: any) => 1.2;

// --- Component Implementation ---

interface InstructorDetailModalProps {
  instructor: InstructorPerformance;
  onCompare: () => void;
  // The `onClose` prop is no longer needed as shadcn/ui handles it
}

export const InstructorDetailModal = ({
  instructor,
  onCompare,
}: InstructorDetailModalProps) => {
  // Prepare weekly pattern data using a memoized stub
  const weeklyData = useMemo(
    () => generateWeeklyPattern(instructor),
    [instructor]
  );

  return (
    <DialogContent className="h-[90vh] max-w-4xl overflow-y-auto">
      <DialogHeader>
        <DialogTitle className="sr-only">{instructor.name} Details</DialogTitle>
        <DialogDescription className="sr-only">
          Detailed performance metrics for {instructor.name}.
        </DialogDescription>
      </DialogHeader>

      {/* Header */}
      <div className="detail-header mb-8 flex items-start justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">
            {instructor.name}
          </h2>
          <p className="instructor-subtitle mt-1 text-base text-gray-600">
            Performance Details
          </p>
        </div>
        <div className="header-actions flex flex-col items-end gap-3">
          <div className="header-badges flex gap-3">
            <span
              className={`status-badge flex items-center gap-2 rounded-full px-3 py-1 text-sm font-medium status-${instructor.status}`}
            >
              {getStatusIcon(instructor.status)}{' '}
              {formatStatus(instructor.status)}
            </span>
            <span className="performance-badge flex items-center rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800">
              Score: {Math.round(instructor.performanceScore)}/100
            </span>
          </div>
          <button
            className="compare-button flex items-center gap-2 rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm transition-transform hover:scale-105 hover:bg-indigo-700"
            onClick={onCompare}
          >
            <UsersIcon size={18} weight="duotone" />
            Compare
          </button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="metrics-grid mb-8 grid grid-cols-2 gap-6 md:grid-cols-4">
        {/* Fill Rate */}
        <div className="metric-card rounded-lg bg-gray-50 p-4 text-center">
          <div className="metric-header mb-2 flex items-center justify-center gap-2">
            <ChartLineIcon
              size={20}
              weight="duotone"
              className="text-blue-500"
            />
            <h4 className="text-sm font-semibold text-gray-700">Fill Rate</h4>
          </div>
          <ResponsiveContainer width="100%" height={100}>
            <RadialBarChart
              cx="50%"
              cy="50%"
              innerRadius="65%"
              outerRadius="85%"
              data={[{ value: instructor.fillRate, fill: '#3b82f6' }]}
              startAngle={90}
              endAngle={-270}
            >
              <RadialBar
                dataKey="value"
                cornerRadius={10}
                background={{ fill: '#e5e7eb' }}
              />
              <text
                x="50%"
                y="50%"
                textAnchor="middle"
                dominantBaseline="middle"
                className="fill-gray-900 text-2xl font-bold"
              >
                {instructor.fillRate.toFixed(0)}%
              </text>
            </RadialBarChart>
          </ResponsiveContainer>
          <p className="mt-2 text-xs text-gray-600">
            Avg class capacity utilization
          </p>
        </div>

        {/* Client Retention */}
        <div className="metric-card rounded-lg bg-gray-50 p-4 text-center">
          <div className="metric-header mb-2 flex items-center justify-center gap-2">
            <UsersIcon size={20} weight="duotone" className="text-green-500" />
            <h4 className="text-sm font-semibold text-gray-700">
              Client Retention
            </h4>
          </div>
          <ResponsiveContainer width="100%" height={100}>
            <RadialBarChart
              cx="50%"
              cy="50%"
              innerRadius="65%"
              outerRadius="85%"
              data={[{ value: instructor.retention, fill: '#10b981' }]}
              startAngle={90}
              endAngle={-270}
            >
              <RadialBar
                dataKey="value"
                cornerRadius={10}
                background={{ fill: '#e5e7eb' }}
              />
              <text
                x="50%"
                y="50%"
                textAnchor="middle"
                dominantBaseline="middle"
                className="fill-gray-900 text-2xl font-bold"
              >
                {instructor.retention.toFixed(0)}%
              </text>
            </RadialBarChart>
          </ResponsiveContainer>
          <p className="mt-2 text-xs text-gray-600">
            New clients returning within quarter
          </p>
        </div>

        {/* Client Satisfaction */}
        <div className="metric-card rounded-lg bg-gray-50 p-4 text-center">
          <div className="metric-header mb-2 flex items-center justify-center gap-2">
            <StarIcon size={20} weight="duotone" className="text-yellow-500" />
            <h4 className="text-sm font-semibold text-gray-700">
              Client Rating
            </h4>
          </div>
          <div className="flex h-[100px] flex-col items-center justify-center">
            <div className="text-4xl font-bold text-yellow-500">
              {instructor.satisfaction.toFixed(1)}
            </div>
            <div className="mt-1 flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <StarIcon
                  key={star}
                  size={16}
                  weight={star <= instructor.satisfaction ? 'fill' : 'duotone'}
                  className="text-yellow-400"
                />
              ))}
            </div>
          </div>
          <p className="mt-2 text-xs text-gray-600">
            Average client satisfaction
          </p>
        </div>

        {/* New Client Acquisition */}
        <div className="metric-card rounded-lg bg-gray-50 p-4 text-center">
          <div className="metric-header mb-2 flex items-center justify-center gap-2">
            <UserPlusIcon
              size={20}
              weight="duotone"
              className="text-indigo-500"
            />
            <h4 className="text-sm font-semibold text-gray-700">New Clients</h4>
          </div>
          <div className="flex h-[100px] flex-col items-center justify-center">
            <div className="text-4xl font-bold text-indigo-600">
              {instructor.acquisition}
            </div>
            <div className="text-sm text-gray-600">this quarter</div>
          </div>
          <p className="mt-2 text-xs text-gray-600">
            First-time studio clients
          </p>
        </div>
      </div>

      {/* 30-Day Performance Trend */}
      <div className="trend-section mb-8">
        <h3 className="mb-4 text-xl font-semibold text-gray-900">
          30-Day Performance Trend
        </h3>
        <ResponsiveContainer width="100%" height={200}>
          <AreaChart data={instructor.last30Days}>
            <defs>
              <linearGradient id="scoreGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8} />
                <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <XAxis dataKey="date" tick={{ fontSize: 12 }} />
            <YAxis domain={[0, 100]} tick={{ fontSize: 12 }} />
            <Tooltip />
            <Area
              type="monotone"
              dataKey="score"
              stroke="#8b5cf6"
              fill="url(#scoreGradient)"
              strokeWidth={2}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>

      {/* Weekly Pattern & Insights */}
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div className="pattern-section">
          <h3 className="mb-4 text-xl font-semibold text-gray-900">
            Weekly Class Performance
          </h3>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={weeklyData}>
              <XAxis dataKey="day" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip />
              <Legend wrapperStyle={{ fontSize: '14px' }} />
              <Bar
                dataKey="morning"
                stackId="a"
                fill="#818cf8"
                name="Morning"
              />
              <Bar
                dataKey="afternoon"
                stackId="a"
                fill="#3b82f6"
                name="Afternoon"
              />
              <Bar
                dataKey="evening"
                stackId="a"
                fill="#1e40af"
                name="Evening"
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
        <div className="insights-section">
          <h3 className="mb-4 text-xl font-semibold text-gray-900">
            Key Insights
          </h3>
          <div className="insight-cards flex flex-col gap-4">
            <div className="insight-card flex items-center gap-4 rounded-lg bg-gray-50 p-4">
              <ClockIcon size={24} weight="duotone" className="text-blue-500" />
              <p className="text-sm text-gray-700">
                Best performance during{' '}
                <strong className="text-gray-900">
                  {getBestTimeSlot(weeklyData)}
                </strong>{' '}
                classes
              </p>
            </div>
            <div className="insight-card flex items-center gap-4 rounded-lg bg-gray-50 p-4">
              <TrendUpIcon
                size={24}
                weight="duotone"
                className="text-green-500"
              />
              <p className="text-sm text-gray-700">
                Month-over-month change:{' '}
                <strong
                  className={
                    instructor.monthOverMonthChange >= 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }
                >
                  {instructor.monthOverMonthChange > 0 ? '+' : ''}
                  {instructor.monthOverMonthChange.toFixed(1)}%
                </strong>
              </p>
            </div>
            <div className="insight-card flex items-center gap-4 rounded-lg bg-gray-50 p-4">
              <UsersIcon
                size={24}
                weight="duotone"
                className="text-indigo-500"
              />
              <p className="text-sm text-gray-700">
                Retention rate is{' '}
                <strong className="text-gray-900">
                  {getRetentionComparison(instructor)}x
                </strong>{' '}
                studio average
              </p>
            </div>
        </div>
      </div>
    </div>
    </DialogContent>
  );
};
