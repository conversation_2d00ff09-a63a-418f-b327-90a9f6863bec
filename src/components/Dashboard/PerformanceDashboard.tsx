import React, { useState } from 'react';
import { Dialog } from '@/components/ui/dialog';
import { usePreparedData } from '@/hooks/usePreparedData';
import PerformanceGalaxy from './PerformanceGalaxy';
import { InstructorDetailModal } from './InstructorDetailModal';
import { StatusBar } from './StatusBar';
import type { InstructorPerformance } from '@/types/instructor';

/**
 * A dashboard component to visualize instructor performance data.
 * This is the main component that orchestrates the entire dashboard.
 */
const PerformanceDashboard = () => {
  const [selectedInstructor, setSelectedInstructor] =
    useState<InstructorPerformance | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [compareList, setCompareList] = useState<InstructorPerformance[]>([]);
  const preparedData = usePreparedData();

  const handleBubbleClick = (instructor: InstructorPerformance) => {
    setSelectedInstructor(instructor);
    setIsModalOpen(true);
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <div className="flex h-full flex-col gap-4 p-4">
        <header className="flex-shrink-0">
          <h1 className="text-2xl font-bold">Performance Dashboard</h1>
        </header>

        <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
          <div className="lg:col-span-3">
            <StatusBar data={preparedData} />
          </div>
          <main className="lg:col-span-2">
            <PerformanceGalaxy
              instructors={preparedData}
              onInstructorSelect={handleBubbleClick}
              compareList={compareList}
              onCompareListChange={setCompareList}
            />
          </main>
          <aside className="lg:col-span-1">
            {/* Potential future component for top/bottom performers list */}
          </aside>
        </div>
      </div>
      {selectedInstructor && (
        <InstructorDetailModal
          instructor={selectedInstructor}
          onCompare={() => {
            // Placeholder for comparison logic
            console.log('Compare clicked for:', selectedInstructor.name);
          }}
        />
      )}
    </Dialog>
  );
};

export default PerformanceDashboard;
