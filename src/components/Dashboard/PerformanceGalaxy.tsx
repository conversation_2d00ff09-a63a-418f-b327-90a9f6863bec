import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Axis,
  <PERSON>ltip,
  ResponsiveContainer,
  ReferenceLine,
  ReferenceArea,
} from 'recharts';
import {
  InfoIcon,
  StarIcon,
  RocketLaunchIcon,
  EqualsIcon,
  WarningIcon,
} from '@phosphor-icons/react';
import { type InstructorPerformance } from '../../types/instructor';
import { CustomBubble } from './CustomBubble';
import { CustomTooltip } from './CustomTooltip';

// --- Component Implementation ---

interface PerformanceGalaxyProps {
  instructors: InstructorPerformance[];
  onInstructorSelect: (instructor: InstructorPerformance) => void;
  compareList: InstructorPerformance[];
  onCompareListChange: (list: InstructorPerformance[]) => void;
}

const PerformanceGalaxy = ({
  instructors,
  onInstructorSelect,
  compareList,
  onCompareListChange,
}: PerformanceGalaxyProps) => {
  const [hoveredInstructor, setHoveredInstructor] = useState(null);

  // Transform data for scatter plot
  const scatterData = instructors.map((inst) => ({
    x: inst.fillRate,
    y: inst.retention,
    z: inst.satisfaction, // This controls bubble size
    ...inst,
  }));

  // Performance zones
  const performanceZones = [
    {
      x1: 0,
      x2: 50,
      y1: 0,
      y2: 50,
      fill: 'rgba(239, 68, 68, 0.05)',
      label: 'Needs Support',
    },
    {
      x1: 50,
      x2: 100,
      y1: 0,
      y2: 50,
      fill: 'rgba(251, 146, 60, 0.05)',
      label: 'Improve Retention',
    },
    {
      x1: 0,
      x2: 50,
      y1: 50,
      y2: 100,
      fill: 'rgba(59, 130, 246, 0.05)',
      label: 'Boost Fill Rate',
    },
    {
      x1: 50,
      x2: 100,
      y1: 50,
      y2: 100,
      fill: 'rgba(34, 197, 94, 0.05)',
      label: 'Star Zone',
    },
  ];

  // Studio averages
  const avgFillRate =
    instructors.reduce((sum, inst) => sum + inst.fillRate, 0) /
      (instructors.length || 1) || 50;
  const avgRetention =
    instructors.reduce((sum, inst) => sum + inst.retention, 0) /
      (instructors.length || 1) || 50;

  // biome-ignore lint/suspicious/noExplicitAny: This is a Recharts event handler
  const handleBubbleClick = (data: any) => {
    if (compareList.length > 0) {
      // In compare mode
      const isSelected = compareList.some(
        (inst) => inst.instructorId === data.instructorId
      );
      if (isSelected) {
        onCompareListChange(
          compareList.filter((inst) => inst.instructorId !== data.instructorId)
        );
      } else if (compareList.length < 4) {
        onCompareListChange([...compareList, data]);
      }
    } else {
      // Normal selection mode
      onInstructorSelect(data);
    }
  };

  return (
    <div className="galaxy-container p-6 bg-white flex-grow flex flex-col">
      <div className="galaxy-header mb-6">
        <h2 className="text-xl font-semibold text-gray-800">
          Performance Constellation
        </h2>
        <div className="galaxy-legend flex items-center gap-4 text-sm text-gray-600">
          <span className="flex items-center gap-2">
            <StarIcon size={16} weight="duotone" className="text-yellow-500" />
            Top Performer
          </span>
          <span className="flex items-center gap-2">
            <RocketLaunchIcon
              size={16}
              weight="duotone"
              className="text-green-500"
            />
            Rising Star
          </span>
          <span className="flex items-center gap-2">
            <EqualsIcon size={16} weight="duotone" className="text-blue-500" />
            Stable
          </span>
          <span className="flex items-center gap-2">
            <WarningIcon size={16} weight="duotone" className="text-red-500" />
            Needs Attention
          </span>
        </div>
      </div>

      <div className="flex-grow w-full h-full">
        <ResponsiveContainer width="100%" height="100%">
          <ScatterChart
            margin={{ top: 20, right: 30, bottom: 40, left: 30 }}
            onClick={(e) =>
              e &&
              e.activePayload &&
              handleBubbleClick(e.activePayload[0].payload)
            }
          >
            {/* Background zones */}
            {performanceZones.map((zone) => (
              <ReferenceArea key={zone.label} {...zone} strokeOpacity={0} />
            ))}

            {/* Studio average lines */}
            <ReferenceLine
              x={avgFillRate}
              stroke="#9ca3af"
              strokeDasharray="3 3"
            />
            <ReferenceLine
              y={avgRetention}
              stroke="#9ca3af"
              strokeDasharray="3 3"
            />

            {/* Axes */}
            <XAxis
              type="number"
              dataKey="x"
              name="Fill Rate"
              unit="%"
              domain={[0, 100]}
              label={{
                value: 'Fill Rate →',
                position: 'insideBottom',
                offset: -15,
                className: 'text-gray-500 font-medium text-sm',
              }}
              tick={{ fill: '#6b7280', fontSize: 12 }}
            />
            <YAxis
              type="number"
              dataKey="y"
              name="Client Retention"
              unit="%"
              domain={[0, 100]}
              label={{
                value: 'Client Retention',
                angle: -90,
                position: 'insideLeft',
                offset: -10,
                className: 'text-gray-500 font-medium text-sm',
              }}
              tick={{ fill: '#6b7280', fontSize: 12 }}
            />
            <ZAxis
              type="number"
              dataKey="z"
              domain={[1, 5]}
              range={[100, 600]} // Bubble size range based on rating
              name="Rating"
            />

            <Tooltip
              content={<CustomTooltip />}
              cursor={{ strokeDasharray: '3 3' }}
            />

            <Scatter
              name="Instructors"
              data={scatterData}
              shape={
                <CustomBubble
                  compareMode={compareList.length > 0}
                  compareList={compareList}
                />
              }
            />
          </ScatterChart>
        </ResponsiveContainer>
      </div>

      {compareList.length > 0 && (
        <div className="compare-mode-indicator flex items-center justify-center gap-2 mt-4 p-3 bg-indigo-100 text-indigo-700 rounded-lg">
          <InfoIcon size={20} weight="duotone" />
          <span>
            Select up to 4 instructors to compare ({compareList.length}/4)
          </span>
        </div>
      )}
    </div>
  );
};

export default PerformanceGalaxy;
