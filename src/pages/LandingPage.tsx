import { SignUp<PERSON>utton, SignInButton } from "@clerk/clerk-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export default function LandingPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center p-8">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
          Instructor Performance Dashboard
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Unlock data-driven insights into instructor performance.
        </p>
        <div className="flex justify-center gap-4">
          <Button size="lg" asChild>
            <SignInButton mode="modal" />
          </Button>
          <Button size="lg" variant="outline" asChild>
            <SignUpButton mode="modal" />
          </Button>
        </div>
      </div>
    </div>
  );
}
