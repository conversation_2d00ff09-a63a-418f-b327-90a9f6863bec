import { action, internalMutation } from './_generated/server';
import { v } from 'convex/values';
import { internal } from './_generated/api';

const DELETION_BATCH_SIZE = 2000; // Much larger batches for faster deletion
const INSERTION_BATCH_SIZE = 1000; // Larger insertion batches too

// --- Internal Mutations: These perform the raw database operations ---

/**
 * [INTERNAL] Deletes a single batch of documents. Called by actions.
 */
export const deleteBatch = internalMutation({
  args: { tableName: v.string() },
  handler: async (ctx, { tableName }) => {
    // Validate table name exists
    const validTables = [
      'instructors',
      'classes',
      'attendance_records',
      'ratings',
      'benchmarks',
      'clients',
    ];

    if (!validTables.includes(tableName)) {
      throw new Error(`Invalid table name: ${tableName}`);
    }

    const documents = await ctx.db
      .query(tableName as any)
      .take(DELETION_BATCH_SIZE);
    await Promise.all(documents.map((doc) => ctx.db.delete(doc._id)));
    return documents.length;
  },
});

/**
 * [INTERNAL] Inserts a single batch of documents. Called by actions.
 */
export const batchInsert = internalMutation({
  args: { tableName: v.string(), data: v.array(v.any()) },
  handler: async (ctx, { tableName, data }) => {
    // Validate table name exists
    const validTables = [
      'instructors',
      'classes',
      'attendance_records',
      'ratings',
      'benchmarks',
      'clients',
    ];

    if (!validTables.includes(tableName)) {
      throw new Error(`Invalid table name: ${tableName}`);
    }

    await Promise.all(data.map((doc) => ctx.db.insert(tableName as any, doc)));
  },
});

// --- Public Actions: These are the entry points for the client script ---

/**
 * [ACTION] Clears a table completely by looping on the server.
 * Optimized for speed with larger batches and parallel processing.
 */
export const clearTable = action({
  args: { tableName: v.string() },
  handler: async (ctx, { tableName }) => {
    let deletedCount;
    let totalDeleted = 0;

    // Run multiple deletion batches in parallel for speed
    const maxParallel = 3;

    do {
      const deletionPromises = [];
      for (let i = 0; i < maxParallel; i++) {
        deletionPromises.push(
          ctx.runMutation(internal.seed.deleteBatch, { tableName })
        );
      }

      const results = await Promise.all(deletionPromises);
      deletedCount = results.reduce((sum, count) => sum + count, 0);
      totalDeleted += deletedCount;
    } while (deletedCount > 0);

    return { totalDeleted };
  },
});

/**
 * [ACTION] Clears all tables at once - much faster than individual table clearing.
 */
export const clearAllTables = action({
  args: {},
  handler: async (ctx) => {
    const tables = [
      'benchmarks',
      'ratings',
      'attendance_records',
      'classes',
      'instructors',
      'clients',
    ];

    const results: Record<string, number> = {};

    // Run all table clears in parallel
    const clearPromises = tables.map(async (tableName) => {
      let deletedCount;
      let totalDeleted = 0;

      do {
        const deletionPromises = [];
        for (let i = 0; i < 3; i++) {
          // 3 parallel batches per table
          deletionPromises.push(
            ctx.runMutation(internal.seed.deleteBatch, { tableName })
          );
        }

        const batchResults = await Promise.all(deletionPromises);
        deletedCount = batchResults.reduce((sum, count) => sum + count, 0);
        totalDeleted += deletedCount;
      } while (deletedCount > 0);

      return { tableName, totalDeleted };
    });

    const clearResults = await Promise.all(clearPromises);
    clearResults.forEach(({ tableName, totalDeleted }) => {
      results[tableName] = totalDeleted;
    });

    return results;
  },
});

/**
 * [ACTION] Inserts a single, pre-chunked array of documents into a table.
 */
export const insertChunk = action({
  args: {
    tableName: v.string(),
    data: v.array(v.any()),
  },
  handler: async (ctx, { tableName, data }) => {
    const totalRecords = data.length;

    // Process multiple insertion batches in parallel for speed
    const insertionPromises = [];
    for (let i = 0; i < totalRecords; i += INSERTION_BATCH_SIZE) {
      const chunk = data.slice(i, i + INSERTION_BATCH_SIZE);
      insertionPromises.push(
        ctx.runMutation(internal.seed.batchInsert, {
          tableName: tableName,
          data: chunk,
        })
      );
    }

    await Promise.all(insertionPromises);
    return { inserted: totalRecords };
  },
});
