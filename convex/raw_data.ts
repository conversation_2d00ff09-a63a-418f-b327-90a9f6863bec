import { mutation, internalMutation } from './_generated/server';
import { v } from 'convex/values';

/**
 * Determines the time of day category from a Unix timestamp.
 * @param timestamp - The Unix timestamp in milliseconds.
 * @returns 'am', 'pm', or 'weekend'.
 */
const getTimeOfDay = (timestamp: number): 'am' | 'pm' | 'weekend' => {
  const date = new Date(timestamp);
  const day = date.getDay(); // Sunday = 0, Saturday = 6
  const hour = date.getHours(); // 0-23

  if (day === 0 || day === 6) {
    return 'weekend';
  }
  return hour < 12 ? 'am' : 'pm';
};

// Mutation to upload a single class record
export const uploadClass = mutation({
  args: {
    class_id: v.string(),
    instructor_id: v.string(),
    location: v.string(),
    class_type: v.string(),
    class_timestamp: v.number(),
    capacity: v.number(),
    attendance: v.number(),
  },
  handler: async (ctx, args) => {
    const time_of_day = getTimeOfDay(args.class_timestamp);
    await ctx.db.insert('classes', { ...args, time_of_day });
  },
});

// Mutation to upload a batch of class records
export const uploadClassBatch = mutation({
  args: {
    classes: v.array(
      v.object({
        class_id: v.string(),
        instructor_id: v.string(),
        location: v.string(),
        class_type: v.string(),
        class_timestamp: v.number(),
        capacity: v.number(),
        attendance: v.number(),
      })
    ),
  },
  handler: async (ctx, args) => {
    for (const classRecord of args.classes) {
      const time_of_day = getTimeOfDay(classRecord.class_timestamp);
      await ctx.db.insert('classes', { ...classRecord, time_of_day });
    }
  },
});

// Mutation to upload a single attendance record
export const uploadAttendanceRecord = mutation({
  args: {
    client_id: v.string(),
    class_id: v.string(),
    instructor_id: v.string(),
    class_timestamp: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert('attendance_records', args);
  },
});

// Mutation to upload a batch of attendance records
export const uploadAttendanceRecordBatch = mutation({
  args: {
    records: v.array(
      v.object({
        client_id: v.string(),
        class_id: v.string(),
        instructor_id: v.string(),
        class_timestamp: v.number(),
      })
    ),
  },
  handler: async (ctx, args) => {
    for (const record of args.records) {
      await ctx.db.insert('attendance_records', record);
    }
  },
});

// An internal mutation to clear all raw data tables at once
export const deleteAllRawData = internalMutation({
  handler: async (ctx) => {
    // Note: In a real production app with huge data, you'd do this in batches.
    // For this project, this is fine.

    console.log('Deleting all class records...');
    const classes = await ctx.db.query('classes').collect();
    await Promise.all(classes.map((doc) => ctx.db.delete(doc._id)));

    console.log('Deleting all attendance records...');
    const attendance = await ctx.db.query('attendance_records').collect();
    await Promise.all(attendance.map((doc) => ctx.db.delete(doc._id)));

    console.log('Raw data deleted.');
  },
});
