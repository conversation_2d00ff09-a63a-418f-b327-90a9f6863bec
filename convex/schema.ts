import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

export default defineSchema({
  // REVISED: instructor_id is a string.
  instructors: defineTable({
    instructor_id: v.string(),
    name: v.string(),
  }).index('by_instructor_id', ['instructor_id']),

  // REVISED: All IDs are strings.
  classes: defineTable({
    class_id: v.string(),
    instructor_id: v.string(),
    location: v.string(),
    class_type: v.string(),
    class_timestamp: v.number(),
    time_of_day: v.union(
      v.literal('am'),
      v.literal('pm'),
      v.literal('weekend')
    ),
    capacity: v.number(),
    attendance: v.number(),
  }).index('by_instructor_id', ['instructor_id']),

  // REVISED: All IDs are strings.
  attendance_records: defineTable({
    client_id: v.string(), // REVISED
    class_id: v.string(),
    instructor_id: v.string(),
    class_timestamp: v.number(),
  })
    .index('by_client_id_and_time', ['client_id', 'class_timestamp'])
    .index('by_instructor_id', ['instructor_id']),

  // REVISED: All IDs are strings.
  ratings: defineTable({
    class_id: v.string(),
    instructor_id: v.string(),
    client_id: v.string(), // REVISED
    class_timestamp: v.number(),
    rating: v.number(),
  }).index('by_instructor_id', ['instructor_id']),

  // This table for benchmarks does not contain IDs and is unchanged.
  benchmarks: defineTable({
    quarter: v.string(),
    metric: v.string(),
    filterKey: v.string(),
    value: v.number(),
  }).index('by_key', ['quarter', 'metric', 'filterKey']),

  // NEW TABLE: All IDs are strings.
  clients: defineTable({
    client_id: v.string(), // REVISED
    name: v.string(),
    first_visit_timestamp: v.number(),
  }).index('by_client_id', ['client_id']),
});
