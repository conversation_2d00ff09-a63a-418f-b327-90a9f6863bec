import { query } from './_generated/server';
import { v } from 'convex/values';

// This query fetches all raw data for the dashboard within a 90-day window.
export const getDashboardData = query({
  args: {},
  handler: async (ctx) => {
    const ninetyDaysAgo = Date.now() - 90 * 24 * 60 * 60 * 1000;

    const instructors = await ctx.db.query('instructors').collect();
    const recentClasses = await ctx.db
      .query('classes')
      .filter((q) => q.gt(q.field('class_timestamp'), ninetyDaysAgo))
      .collect();
    const recentRatings = await ctx.db.query('ratings').collect(); // In a real app, filter this by timestamp too.
    const recentAttendance = await ctx.db.query('attendance_records').collect(); // In a real app, this would need to be optimized.

    // NOTE: For a production app with large datasets, fetching all attendance
    // is not scalable. This would be replaced by more targeted queries or aggregations.
    // For this implementation, we will perform joins/lookups on the client.

    return {
      instructors,
      classes: recentClasses,
      ratings: recentRatings,
      attendance: recentAttendance,
    };
  },
});

// This query fetches the calculated benchmarks.
export const getBenchmarks = query({
  args: { quarter: v.string(), filterKeys: v.array(v.string()) },
  handler: async (ctx, args) => {
    const benchmarks = new Map<string, number>();
    for (const key of args.filterKeys) {
      const metric = key.split(':')[0];
      const filter = key.split(':').slice(1).join(':');

      const benchmark = await ctx.db
        .query('benchmarks')
        .withIndex('by_key', (q) =>
          q
            .eq('quarter', args.quarter)
            .eq('metric', metric)
            .eq('filterKey', filter)
        )
        .first();

      if (benchmark) {
        benchmarks.set(key, benchmark.value);
      }
    }
    return Object.fromEntries(benchmarks);
  },
});

export const getPerformanceDashboardData = query({
  handler: async (ctx) => {
    // TODO: This currently fetches all instructors. In a real-world scenario
    // with a large dataset, you would implement pagination or more specific
    // filtering based on user roles or permissions.
    return await ctx.db.query('instructors').collect();
  },
});
