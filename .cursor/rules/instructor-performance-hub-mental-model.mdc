---
description: 
globs: 
alwaysApply: false
---
# Instructor Performance Hub: Mental Model

This document provides a comprehensive overview of the Instructor Performance Hub application, compiled from the perspectives of product management, software architecture, and development. Its purpose is to serve as a foundational "mental model" for all stakeholders involved in the project.

---

## 1. Application Overview (Product Management Perspective)

This section focuses on the "what" and "why" of the application, defining its purpose, target users, and core value proposition.

### 1.1. Purpose & Value Proposition

The **Instructor Performance Hub** is a specialized, internal web application designed for "The Handle Bar," a boutique fitness studio chain. Its primary purpose is to provide data-driven insights into instructor performance.

By aggregating and analyzing various metrics, the application moves beyond simple attendance tracking to quantify an instructor's true impact on client retention and class success. The core value proposition is to empower studio management with objective, actionable data to make informed decisions regarding staffing, training, and scheduling, ultimately enhancing class quality and business growth.

### 1.2. User Personas

The primary user of this application is a **Studio Manager** or **Regional Director**.

- **Role:** Manages one or more studio locations. Responsible for instructor scheduling, hiring, and performance reviews.
- **Goals:**
  - Quickly identify top-performing and under-performing instructors.
  - Understand the key drivers of performance (e.g., "client conversion").
  - Make objective, data-backed decisions about instructor development and class assignments.
  - Monitor the overall health of instructor staff across their locations.
- **Pain Points (Implied):**
  - Decisions based on subjective feedback or simple attendance numbers can be biased or inaccurate.
  - Difficulty in quantifying an instructor's true "performance" beyond "popularity."
  - Lack of a centralized view to compare instructor effectiveness across different class types or locations.

### 1.3. Core Features

- **Authentication:** Secure login for authorized personnel using Clerk.
- **Dashboard KPI View:** At-a-glance view of key metrics:
  - Total number of active instructors.
  - Average "True Performance Score" across all instructors.
  - Average "Client Conversion Rate" across all instructors.
- **Performance Visualization:** A bar chart displaying the top 15 instructors ranked by their calculated performance score, allowing for quick identification of high-performers.
- **Detailed Instructor Table:** A comprehensive, sortable table listing all instructors and their key metrics:
  - Instructor Name
  - True Performance Score (a calculated, externally-derived metric).
  - Client Conversion Rate (percentage of new clients who become regulars).

### 1.4. High-Level User Flow

1.  **Login:** A manager navigates to the application URL. They are presented with a landing page and use the sign-in modal to authenticate via Clerk.
2.  **Dashboard Review:** Upon successful login, the user is directed to the main `DashboardPage`. They immediately see the high-level KPIs and the chart of top performers.
3.  **Detailed Analysis:** The manager scrolls down to the `InstructorTable` to view data for all instructors. They can sort the table by name, performance score, or conversion rate to analyze the data from different angles.
4.  **Logout:** The manager clicks the user button in the header to sign out, ending their session.

---

## 2. Software Architecture (Architect's Perspective)

This section describes the system's structure, its components, their interactions, and the underlying data models.

### 2.1. Architectural Style

The application employs a **Serverless, Backend-as-a-Service (BaaS) architecture**. It is a modern **Single Page Application (SPA)** on the frontend, powered by a reactive, serverless backend.

- **Frontend:** A React-based SPA built with Vite and TypeScript. It is responsible for all rendering, UI logic, and client-side state.
- **Backend:** [Convex](mdc:https:/www.convex.dev) provides the serverless database, real-time data synchronization, and backend functions (queries/mutations). This eliminates the need for a traditional, stateful server application.
- **Authentication:** [Clerk](mdc:https:/clerk.com) is used as a third-party identity provider, handling user management and authentication, which is tightly integrated with Convex's authorization layer.
- **Data Pipeline:** An offline, script-based data ingestion process exists, suggesting that complex data processing and metric calculation happens outside the core application before being loaded into the backend.

### 2.2. System & Component Diagrams

#### System Architecture Overview

This diagram illustrates the high-level components and their relationships.

```mermaid
graph TD
    subgraph User Facing
        User[Studio Manager]
    end

    subgraph Cloud Services
        Frontend[React SPA<br/>(Vite, shadcn/ui, Tailwind)]
        Backend[Convex Backend<br/>(Serverless DB, Queries, Mutations)]
        Auth[Clerk Auth<br/>(User Management, JWT)]
    end

    subgraph Offline Process
        DataProcessing[External Data Science Pipeline<br/>(Generates Metrics)]
        DataFiles(output.json, .csv files)
        UploadScript[upload.mjs script]
    end

    User -->|Interacts with| Frontend
    Frontend -->|Fetches Data & Subscribes| Backend
    Frontend -->|Authenticates via| Auth

    Backend <-->|Validates JWT| Auth

    DataProcessing --> DataFiles
    UploadScript --> |Reads| DataFiles
    UploadScript --> |Writes to| Backend

    style Frontend fill:#61DAFB
    style Backend fill:#9443FF
    style Auth fill:#6C47FF
```

#### Key Data Flows

**1. Offline Data Ingestion Flow:** This flow populates the Convex database with pre-processed performance data.

```mermaid
sequenceDiagram
    participant Ext as External Process
    participant Script as upload.mjs
    participant Convex as Convex Mutations

    Ext->>Script: Generates output.json & raw CSVs
    Script->>Convex: `uploadInstructorDataBatch`
    Script->>Convex: `uploadClassBatch`
    Script->>Convex: `uploadAttendanceRecordBatch`
    Convex-->>Script: Acknowledges writes
```

**2. Dashboard Data Request Flow:** This shows how the frontend retrieves and displays data in real-time.

```mermaid
sequenceDiagram
    participant User
    participant DashboardPage as React Dashboard Page
    participant ConvexHook as Convex `useQuery` Hook
    participant ConvexBackend as Convex Backend

    User->>DashboardPage: Loads page
    DashboardPage->>ConvexHook: Calls `useQuery(api.instructors.get)`
    ConvexHook->>ConvexBackend: Establishes real-time subscription
    ConvexBackend-->>ConvexHook: Pushes initial data & subsequent updates
    ConvexHook-->>DashboardPage: Re-renders component with new data
```

#### Frontend Component Interaction

This diagram shows how the primary UI components on the dashboard are composed and interact.

```mermaid
graph TD
    subgraph Zustand Store
        InstructorStore[(`useInstructorStore`)]
    end

    subgraph Dashboard Page
        DP[DashboardPage]
        DC1[DataCard: Total]
        DC2[DataCard: Avg Perf.]
        DC3[DataCard: Avg Conv.]
        PC[PerformanceChart]
        IT[InstructorTable]
    end

    DP --> |Renders| DC1
    DP --> |Renders| DC2
    DP --> |Renders| DC3
    DP --> |Renders| PC
    DP --> |Renders| IT

    IT --> |Reads sort state & dispatches sort actions| InstructorStore

    style IT fill:#f9f,stroke:#333,stroke-width:2px
    style InstructorStore fill:#f9f,stroke:#333,stroke-width:2px
```

### 2.3. Data Model

The data is structured across three tables within the Convex database, defined in `convex/schema.ts`.

- `instructors`: The primary table for the dashboard. It stores pre-calculated, aggregated metrics for each instructor.
  - `instructor_id: number`
  - `instructor_name: string`
  - `true_performance_score_nb: number`
  - `confidence_interval_nb: array(number)`
  - `client_conversion_rate_pct: number`
- `classes`: Stores raw data for every individual class session taught.
  - `class_id: number`
  - `instructor_id: number`
  - `location: string`
  - `class_type: string`
  - `class_timestamp: number` (Unix ms)
  - `capacity: number`
  - `attendance: number`
- `attendance_records`: A join table mapping clients to the classes they attended.
  - `client_id: number`
  - `class_id: number`

---

## 3. Development Details (Developer's Perspective)

This section covers the technical implementation details, coding patterns, and project structure.

### 3.1. Tech Stack

- **Programming Language:** **TypeScript** is used across the entire stack (frontend, backend, scripts).
- **Package Manager:** **pnpm** is used for dependency management.
- **Frontend:**
  - **Framework:** **React 18**
  - **Build Tool:** **Vite**
  - **UI Components:** **shadcn/ui** (a collection of re-usable components built on Radix UI and Tailwind CSS).
  - **Styling:** **Tailwind CSS** for utility-first styling.
  - **Charting:** **Recharts** for data visualization.
  - **Routing:** **React Router v7** (`react-router-dom`).
  - **Client-side State:** **Zustand** is used for managing UI state (e.g., table sorting).
- **Backend:**
  - **Platform:** **Convex** for database and serverless functions.
- **Authentication:**
  - **Provider:** **Clerk**
- **Linting:** **ESLint** with TypeScript plugins.

### 3.2. Project Structure

The repository is well-organized, with a clear separation of concerns.

```
/
├── convex/              # All backend code
│   ├── _generated/      # Auto-generated Convex types and API helpers
│   ├── instructors.ts   # Queries/mutations for the 'instructors' table
│   ├── raw_data.ts      # Mutations for ingesting raw class/attendance data
│   ├── schema.ts        # The single source of truth for the database schema
│   └── auth.config.ts   # Configuration for the Clerk authentication provider
│
├── public/              # Static assets
├── scripts/             # Standalone Node.js scripts
│   └── upload.mjs       # Script to parse and upload data to Convex
│
├── src/                 # All frontend source code
│   ├── components/      # Reusable React components
│   │   └── ui/          # Unstyled base components from shadcn/ui
│   ├── lib/             # Utility functions (e.g., `cn` for classnames)
│   ├── pages/           # Top-level route components (Dashboard, Landing)
│   ├── store/           # Zustand state management stores
│   ├── App.tsx          # Root component with routing and auth guards
│   └── main.tsx         # Application entry point, providers setup
│
├── package.json         # Project dependencies and scripts
├── pnpm-lock.yaml       # Lockfile for pnpm
└── tsconfig.json        # TypeScript configuration for the project
```

### 3.3. Key Implementation Details

- **Data Persistence & Access:** Data is stored in Convex. The frontend does not interact with the database directly but calls exported query functions (e.g., `api.instructors.get`) using Convex's `useQuery` hook. This hook cleverly provides a real-time, reactive data layer, automatically updating the UI when data changes in the backend without manual refetching logic.
- **Authentication:** The frontend is wrapped in Clerk and Convex providers in `main.tsx`. `App.tsx` uses `<Authenticated>` and `<Unauthenticated>` components from `convex/react` to render different pages based on login state, effectively creating protected routes.
- **Coding Patterns:**
  - The project heavily favors functional React components with hooks.
  - **Component-Driven Development:** The UI is composed of small, reusable components (`DataCard`, `Header`) and larger, feature-specific ones (`InstructorTable`, `PerformanceChart`).
  - **Type Safety:** TypeScript is used consistently, with types generated by Convex for backend functions and the data model, providing end-to-end type safety.
  - **Configuration over Code:** Libraries like Tailwind CSS and `class-variance-authority` (via `shadcn/ui`) are used to create consistent and maintainable UI elements.
- **Build & Deployment:**
  - **Frontend:** The `pnpm build` command uses Vite to bundle the `src` directory into static assets for production.
  - **Backend:** The Convex backend is deployed via the Convex CLI (e.g., `npx convex deploy`).
- **Testing:**
  - **A significant observation is the complete absence of an automated testing setup.** There are no configurations for Jest, Vitest, React Testing Library, or any other testing framework. This is a critical area for future improvement to ensure code quality and prevent regressions.

---

This document should now serve as a solid foundation for understanding the application.
