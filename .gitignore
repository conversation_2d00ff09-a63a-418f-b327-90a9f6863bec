# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?


.env
.env.local
.env.*.local

# Temporary data pipeline directory
# The data is already in Convex, we don't need to commit the generator
/instructor-performance-data

# VSCode settings
.vscode/

# System files
.DS_Store

convex/dev-data/*