{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/datacard.tsx", "./src/components/header.tsx", "./src/components/instructortable.tsx", "./src/components/layout.tsx", "./src/components/performancechart.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/table.tsx", "./src/lib/utils.ts", "./src/pages/dashboardpage.tsx", "./src/pages/landingpage.tsx", "./src/store/useinstructorstore.ts", "./convex/instructors.ts", "./convex/raw_data.ts", "./convex/schema.ts", "./convex/_generated/api.d.ts", "./convex/_generated/datamodel.d.ts", "./convex/_generated/server.d.ts"], "version": "5.8.3"}