{"name": "instructor-performance-hub", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.32.0", "@phosphor-icons/react": "^2.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.8", "convex-helpers": "^0.1.94", "csv-parse": "^5.6.0", "framer-motion": "^12.18.1", "lucide-react": "^0.517.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}