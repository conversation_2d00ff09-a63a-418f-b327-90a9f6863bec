import { ConvexHttpClient } from 'convex/browser';
import { api } from '../convex/_generated/api.js';
import fs from 'fs/promises';
import path from 'path';

// --- Configuration ---
// IMPORTANT: This value is read from the `.env.local` file created by `npx convex dev`
const CONVEX_URL_DEV = 'https://reliable-goldfinch-19.convex.cloud';

// TODO: You must fill this in with your production deployment URL
const CONVEX_URL_PROD = 'https://proper-chameleon-909.convex.cloud';

const DATA_PATH = 'convex/dev-data';

// Convex has a limit of 8192 array elements per action argument. We'll stay under that.
const CLIENT_SIDE_CHUNK_SIZE = 7000; // Increased chunk size for faster uploads

// The order matters for relational data.
// Clear in reverse dependency order, seed in dependency order
const TABLES = [
  'clients',
  'instructors',
  'classes',
  'attendance_records',
  'ratings',
  'benchmarks',
];
// --- End Configuration ---

async function main() {
  const isProd = process.argv.includes('--prod');
  const convexUrl = isProd ? CONVEX_URL_PROD : CONVEX_URL_DEV;

  if (!convexUrl) {
    console.error('❌ Error: CONVEX_URL is not set. Please update the script.');
    process.exit(1);
  }

  console.log(
    `🚀 Starting robust seed script for ${
      isProd ? 'PRODUCTION' : 'DEVELOPMENT'
    }...`
  );
  console.log(`  > Target URL: ${convexUrl}`);
  console.log(
    'ℹ️  Check the Convex Dashboard Functions tab for detailed server-side logs.'
  );

  const client = new ConvexHttpClient(convexUrl);

  // --- Step 1: Clear all tables ---
  console.log('\n--- Step 1: Clearing all tables in parallel (FAST) ---');
  try {
    const results = await client.action(api.seed.clearAllTables, {});
    console.log('✅ All tables cleared successfully:');
    Object.entries(results).forEach(([tableName, count]) => {
      console.log(`  > ${tableName}: ${count} records deleted`);
    });
  } catch (e) {
    console.log('❌ FAILED to clear tables.');
    console.error('Error details:', e.message || e);
    if (e.data) {
      console.error('Additional error data:', e.data);
    }
    process.exit(1);
  }

  // --- Step 2: Seed all tables in chunks ---
  console.log('\n--- Step 2: Seeding all tables in chunks ---');
  for (const tableName of TABLES) {
    console.log(`\n  > Seeding table: ${tableName}`);

    const dataPath = path.join(DATA_PATH, `${tableName}.json`);
    let data;
    try {
      const fileContent = await fs.readFile(dataPath, 'utf-8');
      data = JSON.parse(fileContent);
    } catch (e) {
      if (e.code === 'ENOENT') {
        console.log(`    SKIPPED (file not found).`);
        continue;
      }
      throw e;
    }

    const totalRecords = data.length;
    console.log(`    Found ${totalRecords} records. Preparing chunks...`);

    for (let i = 0; i < totalRecords; i += CLIENT_SIDE_CHUNK_SIZE) {
      const chunk = data.slice(i, i + CLIENT_SIDE_CHUNK_SIZE);
      const chunkNumber = i / CLIENT_SIDE_CHUNK_SIZE + 1;
      const totalChunks = Math.ceil(totalRecords / CLIENT_SIDE_CHUNK_SIZE);

      process.stdout.write(
        `    -> Sending chunk ${chunkNumber} of ${totalChunks} (${chunk.length} records)... `
      );
      try {
        const result = await client.action(api.seed.insertChunk, {
          tableName,
          data: chunk,
        });
        console.log(`OK (inserted ${result.inserted} records).`);
      } catch (e) {
        console.log('FAILED.');
        console.error('Error details:', e.message || e);
        if (e.data) {
          console.error('Additional error data:', e.data);
        }
        process.exit(1);
      }
    }
  }

  console.log('\n\n✅ Database seeding complete!');
}

main().catch((e) => {
  console.error('\nAn unexpected top-level error occurred:', e);
  process.exit(1);
});
