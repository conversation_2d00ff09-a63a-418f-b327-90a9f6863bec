#!/bin/bash
#
# ==============================================================================
# Script to Deploy Data to Convex Production Environment
# ==============================================================================
#
# This script performs two main actions:
#   1. (Optional) Clears all existing data from the production tables.
#   2. Uploads all source data from local files to the production database.
#
# PREREQUISITE:
# To run the "Clear Data" step, you must temporarily change the `deleteAll` and
# `deleteAllRawData` functions in your Convex code from `internalMutation`
# to `mutation`. You should change them back immediately after this script is done.
#
# USAGE:
# Run this script from the root of your 'instructor-performance-hub' directory.
#
# ./deploy_data_to_prod.sh
#

# Exit immediately if a command fails
set -e

# --- Configuration ---
# Your production Convex deployment URL, as provided.
PROD_URL="https://proper-chameleon-909.convex.cloud"

# --- Introduction and Confirmation ---
echo "========================================================"
echo " Convex Production Data Deployment"
echo "========================================================"
echo
echo "This script will upload data to the following production deployment:"
echo "  => $PROD_URL"
echo
echo "WARNING: This can be a destructive operation. It is recommended to first"
echo "clear all existing data from production to prevent duplicates."
echo

read -p "Do you want to clear ALL data from production before uploading? (y/N) " -n 1 -r
echo # Move to a new line

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo "--------------------------------------------------------"
    echo "STEP 1: CLEARING PRODUCTION DATA..."
    echo "--------------------------------------------------------"
    echo
    echo "❗ IMPORTANT: This requires 'deleteAll' and 'deleteAllRawData' to be"
    echo "set as 'mutation' instead of 'internalMutation' in your Convex code."
    echo
    
    read -p "Have you changed the mutations to 'mutation'? (Press Enter to continue or Ctrl+C to abort)"
    
    echo "-> Clearing 'instructors' table from production..."
    npx convex run instructors:deleteAll --prod
    
    echo "-> Clearing 'classes' and 'attendance_records' tables from production..."
    npx convex run raw_data:deleteAllRawData --prod
    
    echo "✅ Production data cleared."
    echo
    echo "❗ REMINDER: Change the functions back to 'internalMutation' after this script is done!"
    echo
fi

# --- Data Upload ---
echo "--------------------------------------------------------"
echo "STEP 2: UPLOADING SOURCE DATA TO PRODUCTION..."
echo "--------------------------------------------------------"
echo

echo "-> Pointing upload script to production URL and beginning upload..."
VITE_CONVEX_URL="$PROD_URL" node scripts/upload.mjs

echo
echo "--------------------------------------------------------"
echo "🎉 DEPLOYMENT COMPLETE! 🎉"
echo "--------------------------------------------------------"
echo
echo "All source data has been successfully uploaded to your Convex production database."
echo "Your live application at https://hbperformancehub.com should now have data."