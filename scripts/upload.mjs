import { ConvexHttpClient } from 'convex/browser';
import { api } from '../convex/_generated/api.js';
import * as fs from 'fs/promises';
import { parse } from 'csv-parse/sync';
import dotenv from 'dotenv';
import path from 'path';

// Load .env.local first, then .env
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });
dotenv.config();

const BATCH_SIZE = 500; // Number of records to upload in a single batch

// Helper function to split an array into chunks
function chunk(array, size) {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

// Main function to orchestrate the entire upload process
async function main() {
  const convexUrl = process.env.VITE_CONVEX_URL;
  if (!convexUrl) {
    console.error(
      "Error: VITE_CONVEX_URL is not set. Run 'npx convex dev' first."
    );
    process.exit(1);
  }
  const client = new ConvexHttpClient(convexUrl);

  console.log('Starting data upload process...');

  // --- Part 1: Upload Aggregated Instructor Metrics (for the dashboard) ---
  try {
    console.log(
      '\n[1/3] Reading aggregated instructor data from output.json...'
    );
    const instructorsData = await fs.readFile('output.json', 'utf-8');
    const instructors = JSON.parse(instructorsData);
    console.log(
      `  > Found ${instructors.length} instructor records. Uploading in batches of ${BATCH_SIZE}...`
    );

    const instructorChunks = chunk(instructors, BATCH_SIZE);
    let instructorsUploaded = 0;
    for (const instructorChunk of instructorChunks) {
      await client.mutation(api.instructors.uploadInstructorDataBatch, {
        instructors: instructorChunk,
      });
      instructorsUploaded += instructorChunk.length;
      console.log(
        `  > Uploaded batch... ${instructorsUploaded}/${instructors.length}`
      );
    }
    console.log('  ✅ Instructor metrics uploaded successfully.');
  } catch (e) {
    console.error('  ❌ Failed to upload instructor data:', e);
  }

  // --- Part 2: Upload Raw Class Data ---
  try {
    console.log('\n[2/3] Reading raw class data from classes.csv...');
    const classesCsv = await fs.readFile(
      '../instructor-performance-data/classes.csv',
      'utf-8'
    );
    const classes = parse(classesCsv, {
      columns: true, // Treat the first row as headers
      skip_empty_lines: true,
      cast: true, // Automatically convert strings to numbers/booleans where possible
    });
    console.log(
      `  > Found ${classes.length} class records. Processing and uploading in batches of ${BATCH_SIZE}...`
    );
    // REVISED: Convert IDs to strings and add class_timestamp
    const processedClasses = classes.map((c) => {
      const { class_datetime, ...rest } = c;
      return {
        ...rest,
        class_id: String(c.class_id),
        instructor_id: String(c.instructor_id),
        class_timestamp: new Date(class_datetime).getTime(),
      };
    });

    const classChunks = chunk(processedClasses, BATCH_SIZE);
    let classesUploaded = 0;
    for (const classChunk of classChunks) {
      await client.mutation(api.raw_data.uploadClassBatch, {
        classes: classChunk,
      });
      classesUploaded += classChunk.length;
      console.log(`  > Uploaded batch... ${classesUploaded}/${classes.length}`);
    }
    console.log('  ✅ Raw class data uploaded successfully.');

    // REVISED: Create a map for enriching attendance data
    const classIdToDetailsMap = new Map(
      processedClasses.map((c) => [
        c.class_id,
        {
          instructor_id: c.instructor_id,
          class_timestamp: c.class_timestamp,
        },
      ])
    );
  } catch (e) {
    console.error('  ❌ Failed to upload class data:', e);
  }

  // --- Part 3: Upload Raw Attendance Data ---
  try {
    console.log(
      '\n[3/3] Reading raw attendance data from attendance_records.csv...'
    );
    const attendanceCsv = await fs.readFile(
      '../instructor-performance-data/attendance_records.csv',
      'utf-8'
    );
    const records = parse(attendanceCsv, {
      columns: true,
      skip_empty_lines: true,
      cast: true,
    });
    console.log(
      `  > Found ${records.length} attendance records. Uploading in batches of ${BATCH_SIZE}...`
    );

    // REVISED: Enrich attendance records with details from class data
    const enrichedRecords = records
      .map((r) => {
        const classDetails = classIdToDetailsMap.get(String(r.class_id));
        if (!classDetails) {
          console.warn(
            `    > Skipping attendance record for unknown class_id: ${r.class_id}`
          );
          return null;
        }
        return {
          client_id: String(r.client_id),
          class_id: String(r.class_id),
          instructor_id: classDetails.instructor_id,
          class_timestamp: classDetails.class_timestamp,
        };
      })
      .filter(Boolean); // Remove nulls for records that couldn't be mapped

    const recordChunks = chunk(enrichedRecords, BATCH_SIZE);
    let recordsUploaded = 0;
    for (const recordChunk of recordChunks) {
      await client.mutation(api.raw_data.uploadAttendanceRecordBatch, {
        records: recordChunk,
      });
      recordsUploaded += recordChunk.length;
      console.log(`  > Uploaded batch... ${recordsUploaded}/${records.length}`);
    }

    console.log('  ✅ Raw attendance data uploaded successfully.');
  } catch (e) {
    console.error('  ❌ Failed to upload attendance data:', e);
  }

  console.log('\n🎉 Data upload process complete! 🎉');
}

main().catch(console.error);
