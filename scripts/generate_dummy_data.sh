#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
VENV_DIR=".venv"
OUTPUT_DIR="convex/dev-data"
PYTHON_SCRIPT_NAME="generate_data.py"

# --- Main Script Logic ---

# 1. Check for Python 3
if ! command -v python3 &> /dev/null
then
    echo "Error: python3 is not installed or not in your PATH."
    echo "Please install Python 3 to continue."
    exit 1
fi

echo "✅ Python 3 found."

# 2. Create the output directory
mkdir -p "$OUTPUT_DIR"
echo "✅ Output directory ensured at '$OUTPUT_DIR'."

# 3. Create the Python virtual environment
if [ ! -d "$VENV_DIR" ]; then
    echo "Creating Python virtual environment in '$VENV_DIR'..."
    python3 -m venv "$VENV_DIR"
else
    echo "Python virtual environment already exists."
fi
echo "✅ Virtual environment is ready."

# 4. Install dependencies into the virtual environment
echo "Installing necessary Python packages (Faker)..."
# Redirect output to /dev/null for a cleaner console experience
"$VENV_DIR/bin/pip" install Faker > /dev/null
echo "✅ Dependencies installed."

# 5. Create the Python data generation script using a "here document"
echo "Generating Python script..."
cat > "$PYTHON_SCRIPT_NAME" <<'EOF'
import json
import random
import os
import math
from datetime import datetime, timedelta
from faker import Faker

# --- CONFIGURATION ---
OUTPUT_DIR = "convex/dev-data"
START_DATE = datetime(2024, 1, 1)
END_DATE = datetime(2025, 6, 10)
NUM_INSTRUCTORS = 40
NUM_CLIENTS = 2000
NUM_STUDIOS = 8
CLASS_TYPES = ["Cycle", "Sculpt", "Flow"]
CLASS_TIMES_PER_DAY = [6, 8, 16, 18] # 6am, 8am, 4pm, 6pm
CLASS_CAPACITY = 30
RATING_PROBABILITY = 0.15 # 15% of attendees leave a rating

fake = Faker()

# --- DATA STORAGE ---
instructors = []
clients = {} # Using dict for easy lookup by id
classes = []
attendance_records = []
ratings = []
benchmarks = []

# --- HELPER FUNCTIONS ---
def get_quarter(date_obj):
    return f"Q{(date_obj.month - 1) // 3 + 1} {date_obj.year}"

def get_quarter_range(q_str):
    parts = q_str.split(" ")
    quarter = int(parts[0][1])
    year = int(parts[1])
    start_month = (quarter - 1) * 3 + 1
    start_date = datetime(year, start_month, 1)
    end_year = year
    end_month = start_month + 2
    if end_month > 12:
        end_month -= 12
        end_year += 1
    end_day = 31 if end_month in [1, 3, 5, 7, 8, 10, 12] else 30
    if end_month == 2:
        end_day = 29 if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0) else 28
    
    end_date = datetime(year, end_month, end_day, 23, 59, 59)
    return start_date, end_date

# --- DATA GENERATION ---

def generate_instructors():
    print("Step 1/7: Generating instructors...")
    studio_names = [f"Studio {chr(65 + i)}" for i in range(NUM_STUDIOS)]
    for i in range(NUM_INSTRUCTORS):
        num_studios = random.choice([1, 2])
        assigned_studios = random.sample(studio_names, num_studios)
        instructors.append({
            "instructor_id": f"ins_{i+1}",
            "name": fake.name(),
            # Popularity influences attendance. >1 is popular, <1 is less popular
            "popularity_score": random.uniform(0.75, 1.5),
            "studios": assigned_studios
        })

def generate_classes():
    print("Step 2/7: Generating classes schedule...")
    current_date = START_DATE
    class_id_counter = 1
    studio_names = [f"Studio {chr(65 + i)}" for i in range(NUM_STUDIOS)]

    while current_date <= END_DATE:
        day_of_week = current_date.weekday() # Monday is 0, Sunday is 6

        for studio in studio_names:
            # Find instructors eligible for this studio
            eligible_instructors = [i for i in instructors if studio in i['studios']]
            if not eligible_instructors: continue

            for hour in CLASS_TIMES_PER_DAY:
                instructor = random.choice(eligible_instructors)
                
                # Determine attendance based on various factors
                base_attendance = 0.65 # 65% base
                # Time of day factor
                if hour in [6, 18]: base_attendance *= 1.15 # Morning/evening rush
                else: base_attendance *= 0.9
                # Day of week factor
                if day_of_week in [0, 5, 6]: base_attendance *= 1.1 # Mon, Sat, Sun are popular
                # Instructor popularity
                base_attendance *= instructor['popularity_score']
                
                # Final attendance is capacity * rate, with some randomness
                attendance = math.floor(CLASS_CAPACITY * min(base_attendance, 1.0) * random.uniform(0.85, 1.15))
                attendance = min(max(attendance, 5), CLASS_CAPACITY) # Ensure attendance is realistic

                time_of_day = "weekend"
                if day_of_week < 5: # Weekday
                    time_of_day = "am" if hour < 12 else "pm"
                
                class_timestamp = int(current_date.replace(hour=hour, minute=0, second=0, microsecond=0).timestamp() * 1000)

                classes.append({
                    "class_id": f"cls_{class_id_counter}",
                    "instructor_id": instructor["instructor_id"],
                    "location": studio,
                    "class_type": random.choice(CLASS_TYPES),
                    "class_timestamp": class_timestamp,
                    "time_of_day": time_of_day,
                    "capacity": CLASS_CAPACITY,
                    "attendance": attendance,
                })
                class_id_counter += 1
        current_date += timedelta(days=1)

def generate_clients_and_attendance():
    print("Step 3/7: Generating clients...")
    for i in range(NUM_CLIENTS):
        client_id = f"cli_{i+1}"
        # Define client personas for attendance patterns
        persona = random.choices(["regular", "occasional", "new"], weights=[0.2, 0.5, 0.3], k=1)[0]
        clients[client_id] = {
            "client_id": client_id,
            "name": fake.name(),
            "first_visit_timestamp": None, # Will be backfilled
            "persona": persona,
            "attendance_history": []
        }
    
    print("Step 4/7: Simulating attendance and generating records...")
    client_ids = list(clients.keys())

    for cls in classes:
        # Sort clients: prioritize new clients to ensure they get a first class
        # then regulars, then occasionals. This helps acquisition logic.
        
        client_pool = sorted(client_ids, key=lambda cid: (
            len(clients[cid]["attendance_history"]), # fewer classes first
            clients[cid]["persona"] == "new", # prioritize new
            clients[cid]["persona"] == "regular" # then regulars
        ), reverse=False)

        num_to_attend = cls["attendance"]
        attendees = client_pool[:num_to_attend]
        
        for client_id in attendees:
            record = {
                "client_id": client_id,
                "class_id": cls["class_id"],
                "instructor_id": cls["instructor_id"],
                "class_timestamp": cls["class_timestamp"],
            }
            attendance_records.append(record)
            clients[client_id]["attendance_history"].append(record)

def backfill_client_first_visits():
    print("Step 5/7: Finalizing client data...")
    for client_id, client_data in clients.items():
        if client_data["attendance_history"]:
            first_record = min(client_data["attendance_history"], key=lambda x: x["class_timestamp"])
            clients[client_id]["first_visit_timestamp"] = first_record["class_timestamp"]

def generate_ratings():
    print("Step 6/7: Generating ratings...")
    for record in attendance_records:
        if random.random() < RATING_PROBABILITY:
            ratings.append({
                "class_id": record["class_id"],
                "instructor_id": record["instructor_id"],
                "client_id": record["client_id"],
                "rating": random.randint(1, 5),
                "class_timestamp": record["class_timestamp"]
            })

def calculate_benchmarks():
    print("Step 7/7: Calculating benchmarks...")
    quarters = sorted(list(set(get_quarter(datetime.fromtimestamp(c['class_timestamp']/1000)) for c in classes)))
    
    # Pre-calculate client's first attendance for acquisition metric
    client_first_attendance = {}
    for client_id, client_data in clients.items():
        if client_data['first_visit_timestamp']:
            first_record = min(client_data["attendance_history"], key=lambda x: x["class_timestamp"])
            client_first_attendance[client_id] = first_record
            
    for q_str in quarters:
        q_start, q_end = get_quarter_range(q_str)
        q_start_ts = int(q_start.timestamp() * 1000)
        q_end_ts = int(q_end.timestamp() * 1000)

        for instructor in instructors:
            ins_id = instructor['instructor_id']
            
            # Filter all data for this instructor in this quarter
            ins_classes_q = [c for c in classes if c['instructor_id'] == ins_id and q_start_ts <= c['class_timestamp'] <= q_end_ts]
            ins_ratings_q = [r for r in ratings if r['instructor_id'] == ins_id and q_start_ts <= r['class_timestamp'] <= q_end_ts]
            
            if len(ins_classes_q) < 5: continue # Eligibility check

            # Metric: Fill Rate
            total_attendance = sum(c['attendance'] for c in ins_classes_q)
            total_capacity = sum(c['capacity'] for c in ins_classes_q)
            fill_rate = (total_attendance / total_capacity) * 100 if total_capacity > 0 else 0
            benchmarks.append({"quarter": q_str, "metric": "fill_rate", "filterKey": ins_id, "value": round(fill_rate, 2)})

            # Metric: Satisfaction
            satisfaction = sum(r['rating'] for r in ins_ratings_q) / len(ins_ratings_q) if ins_ratings_q else 0
            benchmarks.append({"quarter": q_str, "metric": "satisfaction", "filterKey": ins_id, "value": round(satisfaction, 2)})

            # Metric: Acquisition
            new_clients_this_q = [
                c for c in clients.values() if c['first_visit_timestamp'] and q_start_ts <= c['first_visit_timestamp'] <= q_end_ts
            ]
            acquired_by_ins = 0
            for client in new_clients_this_q:
                first_rec = client_first_attendance.get(client['client_id'])
                if first_rec and first_rec['instructor_id'] == ins_id:
                    acquired_by_ins += 1
            benchmarks.append({"quarter": q_str, "metric": "acquisition", "filterKey": ins_id, "value": acquired_by_ins})
            
            # Metric: Retention
            new_to_ins_this_q = 0
            retained_by_ins = 0
            client_ids_in_ins_classes_q = {rec['client_id'] for rec in attendance_records if rec['instructor_id'] == ins_id and q_start_ts <= rec['class_timestamp'] <= q_end_ts}

            for cid in client_ids_in_ins_classes_q:
                client_history_with_ins = [r for r in clients[cid]['attendance_history'] if r['instructor_id'] == ins_id]
                if not client_history_with_ins: continue

                first_class_with_ins_ts = min(r['class_timestamp'] for r in client_history_with_ins)
                
                if q_start_ts <= first_class_with_ins_ts <= q_end_ts:
                    new_to_ins_this_q += 1
                    # Check if they had more than one class with this instructor in the quarter
                    classes_with_ins_in_q = [r for r in client_history_with_ins if q_start_ts <= r['class_timestamp'] <= q_end_ts]
                    if len(classes_with_ins_in_q) > 1:
                        retained_by_ins += 1
            
            retention_rate = (retained_by_ins / new_to_ins_this_q) * 100 if new_to_ins_this_q > 0 else 0
            benchmarks.append({"quarter": q_str, "metric": "retention", "filterKey": ins_id, "value": round(retention_rate, 2)})


# --- FILE WRITING ---
def write_files():
    print(f"Writing files to '{OUTPUT_DIR}'...")
    
    # Strip temporary helper data before writing
    final_instructors = [{k:v for k,v in i.items() if k not in ['popularity_score', 'studios']} for i in instructors]
    final_clients = [{k:v for k,v in c.items() if k not in ['persona', 'attendance_history']} for c in clients.values() if c['first_visit_timestamp'] is not None]

    with open(os.path.join(OUTPUT_DIR, "instructors.json"), "w") as f:
        json.dump(final_instructors, f, indent=2)
    with open(os.path.join(OUTPUT_DIR, "clients.json"), "w") as f:
        json.dump(final_clients, f, indent=2)
    with open(os.path.join(OUTPUT_DIR, "classes.json"), "w") as f:
        json.dump(classes, f, indent=2)
    with open(os.path.join(OUTPUT_DIR, "attendance_records.json"), "w") as f:
        json.dump(attendance_records, f, indent=2)
    with open(os.path.join(OUTPUT_DIR, "ratings.json"), "w") as f:
        json.dump(ratings, f, indent=2)
    with open(os.path.join(OUTPUT_DIR, "benchmarks.json"), "w") as f:
        json.dump(benchmarks, f, indent=2)
    print("All files written successfully.")

if __name__ == "__main__":
    generate_instructors()
    generate_classes()
    generate_clients_and_attendance()
    backfill_client_first_visits()
    generate_ratings()
    calculate_benchmarks()
    write_files()

EOF
echo "✅ Python script generated."

# 6. Run the Python script using the virtual environment's interpreter
echo "--- Running Data Generation ---"
"$VENV_DIR/bin/python" "$PYTHON_SCRIPT_NAME"
echo "--- Data Generation Complete ---"

# 7. Clean up the Python script file
rm "$PYTHON_SCRIPT_NAME"
echo "✅ Cleanup complete."
echo ""
echo "Success! Your dummy data has been generated in the '$OUTPUT_DIR' directory."